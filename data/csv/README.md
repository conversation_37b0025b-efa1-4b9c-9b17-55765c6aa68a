# Mock Data CSV Files

This directory contains CSV files with mock data for the TPA API endpoints. The data is designed to support all three API endpoints with realistic insurance policy and claims information based on exactly 3 citizens with comprehensive test coverage.

## File Structure

### 1. `policies.csv`
Contains policy and member information for the **PolicyListSF** endpoint.
- **Records**: 8 members (all active) representing 3 distinct citizens
- **Citizens**: 3 unique citizens (CIT001, CIT002, CIT003) with 2-3 policies each
- **Key Fields**: MemberCode, PolicyNo, CitizenID, Names (TH/EN), InsuranceDetails, etc.
- **Test Scenarios**:
  - Each citizen has policies from different insurers
  - VIP and standard members across different insurers
  - Different insurance companies and plans
  - All members are Principal type for simplified testing

### 2. `payment_details.csv`
Contains payment method information linked to members.
- **Records**: 15 payment methods across 8 members
- **Key Fields**: MemberCode, PaymentMethod, PayeeName, BankAccNo, BankName, Primary
- **Test Scenarios**:
  - 1-2 payment methods per member
  - Different payment types (Bank Transfer, Credit Card, Cash, Direct Debit)
  - Primary and secondary payment methods

### 3. `policy_details.csv`
Contains main benefit information for the **PolicyDetailSF** endpoint.
- **Records**: 8 main benefits (one per member)
- **Key Fields**: MemberCode, MainBenefitCode, MainBenefit, Limits, Balances
- **Test Scenarios**:
  - Different benefit types and amounts
  - Various limit structures
  - Balance tracking

### 4. `benefits.csv`
Contains detailed benefit breakdown for each member.
- **Records**: 17 benefit records with sub-benefits
- **Key Fields**: BenefitCode, SubBenefitCode, Limits, Balances, Combined limits
- **Test Scenarios**:
  - 1-3 benefit types per member
  - Complex limit structures
  - Balance calculations

### 5. `contract_conditions.csv`
Contains contract-level conditions and restrictions.
- **Records**: 12 contract conditions
- **Key Fields**: ConditionType, EffectiveDates, ConditionDetail, Action
- **Test Scenarios**:
  - Different condition types (Exclusion, Waiting Period, VIP Service)
  - All conditions are active
  - Audit trail (CreateBy, ModifiedBy)

### 6. `member_conditions.csv`
Contains member-specific conditions and medical history.
- **Records**: 12 member conditions
- **Key Fields**: ConditionType, EffectiveDates, ConditionDetail, Action
- **Test Scenarios**:
  - Medical conditions and monitoring
  - Special member statuses
  - Health alerts and restrictions

### 7. `claims.csv`
Contains claims history for the **ClaimListSF** endpoint.
- **Records**: 17 claims across 8 members
- **Key Fields**: ClaimNo, ClaimStatus, Amounts, Dates, Diagnosis, Provider
- **Test Scenarios**:
  - All required claim statuses: Approved, Authoried, Open, Paid, Pending, Pending For Approval, Rejected
  - Different claim types: Inpatient, Outpatient, Emergency, Accident
  - Various medical conditions and treatments
  - Different hospitals and providers
  - 1-5 claims per member

## Data Relationships

- **CitizenID** is the unique citizen identifier (CIT001, CIT002, CIT003)
- **MemberCode** is the primary key linking all data (MEM001-MEM008)
- **PolicyNo** groups members under the same policy (POL001-POL008)
- **InsurerCode** provides insurer identification (INS001, INS002, INS003)
- **CompanyCode** provides organizational grouping

## New Data Structure (3 Citizens)

### Citizen CIT001 (สมชาย ใจดี / Somchai Jaidee)
- **MEM001**: INS001 - Standard Plan (POL001)
- **MEM002**: INS002 - VIP Executive Plan (POL002)
- **MEM003**: INS003 - Platinum Life Insurance Plan (POL003)

### Citizen CIT002 (มาลี สวยงาม / Malee Suayngam)
- **MEM004**: INS001 - Premium Plan (POL004)
- **MEM005**: INS002 - High Net Worth Plan (POL005)

### Citizen CIT003 (ปิยะดา สุขใส / Piyada Suksai)
- **MEM006**: INS001 - Family Plan (POL006)
- **MEM007**: INS002 - Young Professional Plan (POL007)
- **MEM008**: INS003 - VIP Family Plan (POL008)

## Test Coverage

The mock data covers all API parameter combinations specified in the requirements:

### PolicyListSF Parameters:
- ✅ INSURER_CODE + CITIZEN_ID
- ✅ INSURER_CODE + POLICY_NO + NAME_TH
- ✅ INSURER_CODE + POLICY_NO + NAME_EN
- ✅ INSURER_CODE + CERTIFICATE_NO + NAME_TH
- ✅ INSURER_CODE + CERTIFICATE_NO + NAME_EN
- ✅ INSURER_CODE + STAFF_NO + NAME_TH
- ✅ INSURER_CODE + STAFF_NO + NAME_EN
- ✅ INSURER_CODE + OTHER_ID
- ✅ INSURER_CODE + NAME_TH
- ✅ INSURER_CODE + NAME_EN

### ClaimListSF Parameters:
- ✅ MEMBER_CODE
- ✅ INSURER_CODE + CITIZEN_ID

### Claim Statuses:
- ✅ Approved
- ✅ Authoried
- ✅ Open
- ✅ Paid
- ✅ Pending
- ✅ Pending For Approval
- ✅ Rejected

## Usage Notes

1. **Date Formats**: All dates are in YYYY-MM-DD format
2. **Active Policies**: Only policies with PlanEffFrom <= today <= PlanEffTo should be returned
3. **Thai/English**: Data includes both Thai and English names/descriptions
4. **Amounts**: All monetary amounts are in Thai Baht
5. **Member Codes**: Follow pattern MEM001, MEM002, etc.
6. **Policy Numbers**: Follow pattern POL001, POL002, etc.

This mock data will be loaded into in-memory data structures when the API starts up, providing realistic test scenarios for all endpoints.
