# TPA API

A FastAPI-based REST API for insurance policy and claims management with mock data.

## Overview

This API provides three endpoints for Third Party Administrator (TPA) operations:

1. **PolicyListSF** - Search and list insurance policies
2. **PolicyDetailSF** - Get detailed policy information including benefits and conditions
3. **ClaimListSF** - List claims history for members

## Features

- ✅ FastAPI with automatic request/response validation
- ✅ Comprehensive mock data in CSV format
- ✅ In-memory data storage for fast responses
- ✅ Complex parameter validation for policy searches
- ✅ Support for Thai and English content
- ✅ Docker containerization ready
- ✅ Structured logging
- ✅ Error handling and validation
- ✅ Complete business logic layer
- ✅ Service-oriented architecture
- ✅ Comprehensive test coverage

## API Endpoints

### 1. PolicyListSF
```
GET /api/PolicyListSF
```
Search policies using various parameter combinations.

### 2. PolicyDetailSF
```
GET /api/PolicyDetailSF?MEMBER_CODE={member_code}
```
Get detailed policy information for a specific member.

### 3. ClaimListSF
```
GET /api/ClaimListSF
```
List claims using member code or insurer code + citizen ID.

## Quick Start

### Local Development

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the API:**
   ```bash
   uvicorn src.main:app --reload --host 0.0.0.0 --port 9000
   ```

3. **Access the API:**
   - API Base URL: http://localhost:9000
   - Health Check: http://localhost:9000/health

### Docker Deployment

```bash
# Build the container
docker build -f docker/Dockerfile -t tpa-api .

# Run the container
docker run -p 9000:9000 tpa-api
```

## Project Structure

```
tpa-api/
├── config/                # Configuration
├── data/csv/              # Mock data in CSV format
├── docker/                # Docker configuration
├── src/                    # Main application code
    ├── api/               # API routes and dependencies
    ├── models/            # Pydantic models (request/response/data)
    ├── services/          # Business logic services
    │   ├── validation_service.py  # Parameter validation
    │   ├── policy_service.py      # Policy business logic
    │   └── claim_service.py       # Claim business logic
    ├── data/              # In-memory data management
    │   ├── mock_data.py           # CSV data loading
    │   ├── policy_data.py         # Policy data indexing
    │   └── claim_data.py          # Claim data indexing
    └── utils/             # Utilities and helpers
└── tests/                 # Test files
```

## Mock Data

The API uses comprehensive mock data stored in CSV files based on exactly 3 citizens:

- **3 citizens** (CIT001, CIT002, CIT003) with 2-3 policies each
- **8 members** representing all citizen-policy combinations
- **17 claims** covering all required statuses and types
- **3 insurance companies** (INS001, INS002, INS003)
- **Complex benefit structures** with limits and balances
- **Contract and member conditions**
- **Multiple payment methods** per member

See `data/csv/README.md` for detailed information about the mock data structure.

## Development

This project follows Python best practices:

- **FastAPI** for modern, fast API development
- **Pydantic** for data validation and serialization
- **Structured logging** for observability
- **Clean architecture** with separation of concerns
- **Type hints** throughout the codebase

## License

This project is for demonstration purposes.
